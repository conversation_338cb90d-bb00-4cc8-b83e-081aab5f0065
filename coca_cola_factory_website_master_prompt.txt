Master Prompt (Hinglish) — Coca‑Cola Bottling/Factory Website + Portals

Goal
- Build a complete enterprise‑grade website and portals ecosystem for a Coca‑Cola bottling/factory brand.
- Tech: HTML, CSS, JavaScript, PHP 8.1+, MySQL 8, shared hosting (cPanel). Lightweight, secure, fast.
- Include: Public brand site, Distributor/Retailer portal, Supplier/Vendor portal, Plant Ops dashboards (lite), Quality & Traceability, Sustainability, Careers/ATS, CMS/Blog, E‑commerce (merch optional), Analytics, SEO, Security.

Key Audiences
- Consumers (product info, campaigns, where to buy)
- Distributors/Retailers (orders, invoices, deliveries)
- Suppliers/Vendors (onboarding, RFQs, POs, compliance)
- Corporate stakeholders (investors, media, regulators)
- Candidates (careers)
- Internal ops (read‑only KPIs, QA/traceability)

High‑Level Objectives
- Public site for brand, products, nutrition, campaigns, CSR/ESG, press, investor info
- Self‑service portals: distributor/retailer and supplier/vendor
- Plant/Ops KPIs (read‑only): OEE, downtime, quality lots, traceability
- Content engine: SEO blog + landing pages
- Optional store: branded merchandise via Razorpay
- Compliance: FSSAI labeling (IN), DPDP/GDPR basics, cookie consent

Architecture
- Vanilla PHP MVC
- public/ (docroot), app/{Controllers,Models,Views,Core}, config/, storage/{logs,uploads,cache,docs}, cron/
- Core libs: Router, DB (PDO), Auth, Session, CSRF, Validator, Mailer (SMTP), Cache, FileUpload, PDF, CSV Export
- .htaccess for pretty URLs; config/config.php for env (no secrets in VCS)

Environments & Constraints
- Shared hosting (cPanel): no daemons, cron for schedules
- PHP 8.1+, MySQL 8, HTTPS enforced
- CDN for editor (TinyMCE/CKEditor), charts (Chart.js), icons (Feather/FontAwesome)

Public Website — Pages
- Home: hero video/image, brand story, featured products, sustainability stats, campaigns, news, CTA
- Brands/Products: listing with filters (brand, pack size, flavor), product detail (images, pack sizes, ingredients, allergens, nutrition facts table, FAQ, downloadable spec PDF)
- Where to Buy: store locator (Google Maps), city/ZIP search, channel filters
- Promotions/Campaigns: current promos, T&Cs, participation flow, QR redemption landing
- Recipes/Mixology (optional): UGC moderation, schema
- Sustainability/ESG: water stewardship, energy, packaging, goals & progress
- CSR & Community: initiatives, reports
- Newsroom/Press: press releases, media kit (logos, brand guidelines), contacts
- Investor Relations (optional public): key facts, reports links
- About Us: history, leadership, plants locations
- Careers: openings, culture, benefits, application form
- Contact: enquiry forms (consumer, distributor interest, media) + Google reCAPTCHA
- Legal: privacy, terms, cookie policy, accessibility
- Blog: listing, post detail with SEO, categories/tags, related posts

Consumer Engagement
- Feedback form (product, experience) with category + attachments
- Loyalty/Rewards (optional simple): account, points, coupons
- QR on bottle deep link: campaign landing, unique code validation (optional)

Distributor/Retailer Portal
- Registration (with approval), KYC fields (GSTIN/PAN, address, docs upload)
- Catalog with distributor pricing tiers
- Place Orders: cart, min order qty, delivery slot preference, special instructions
- Order Management: statuses (Pending/Confirmed/Packed/Shipped/Delivered/Cancelled)
- Invoices & Payments: view/download PDF, payment status; offline payments note
- Promotions: trade schemes, credit notes record
- Delivery & Logistics: shipment tracking (manual updates/import), POD upload
- Support: ticketing (basic), announcements

Supplier/Vendor Portal
- Registration & Pre‑qualification: company details, categories, certificates (FSSAI, ISO), Code of Conduct acceptance
- RFQ/RFP: admin posts RFQ, suppliers submit quotes (price, lead time, docs)
- Purchase Orders: view PO, acknowledge, upload ASN (advanced shipping notice)
- Invoices: submit invoice PDFs, status updates (Received/In Review/Approved/Paid)
- Compliance: policy docs, training acknowledgments, audit checklist submissions

Plant Operations (Lite, Read‑Only)
- Dashboard: OEE, line speed, downtime reasons, batch throughput (manual CSV import or simple API placeholder)
- Quality Batches: lot creation, QC checks (pH, Brix, temp), pass/fail with COA PDF upload
- Traceability: product lot → line → date/time → ingredients lots linkage
- Alerts: threshold breaches log (manual entry or file import)

Quality & Traceability
- Batch/LOT master, test results, COA repository
- Recall workflow: mark affected lots, impacted products/shipments (trace graph), public notice draft template

CMS/Blog & Page Builder
- WYSIWYG editor, media library, reusable blocks (CTA, comparison table, FAQs)
- SEO fields: title, meta desc, slug, canonical, OG/Twitter, schema JSON
- Scheduling, drafts, version history, authoring roles
- Keyword Blog Generator (admin tool): keyword → outline (H2/H3), FAQs JSON‑LD, internal link suggestions → Draft
- Sitemap.xml + RSS + robots.txt editor

E‑Commerce (Optional — Merch Store)
- Product catalog (merch), cart, checkout, Razorpay Standard
- Orders, payments, refunds record, invoices (PDF)

Careers / ATS (Lite)
- Job postings (dept, location, FT/PT), apply form with resume upload
- Applicant pipeline: New → Screen → Interview → Offer → Hired/Reject
- Email templates, notes, tags, export CSV

Admin Panel
- Dashboard: KPIs (site traffic, distributor orders, supplier RFQs, QA pass rate, OEE trend, merch sales)
- Users & Roles: Admin, Manager, Editor, Support, Distributor, Supplier
- Content: pages, blog posts, media, redirects (301), menus
- Products: beverages master (public catalog) with nutrition facts, allergens, SKU codes
- Campaigns: create promos, landing pages, QR rules (if enabled)
- Distributor Management: accounts, pricing tiers, credit limit field, orders, invoices
- Supplier Management: accounts, categories, RFQs, quotes, POs, invoices, compliance docs
- Plant/QA: batches, QC tests, COAs, traceability map
- Merch Store: products, stock (simple), coupons, orders, payments
- Reports: sales (merch), distributor orders, supplier spend, QA pass rate, OEE trend, campaign conversions
- Settings: site, branding, emails, SMTP, GA4, Search Console, Maps, Razorpay keys, reCAPTCHA, cookie banner, legal text
- Tools: cache clear, DB backup (dump), import/export CSV, email queue

Security & Privacy
- PHP PDO prepared statements; CSRF tokens; output escaping; HTML Purifier for rich content
- Password hashing (password_hash), secure cookies, session regen on login
- Rate limiting on auth forms; reCAPTCHA v2/v3 configurable
- File uploads: MIME/size validation, image processing (server), store outside docroot if possible
- RBAC enforcement; audit log of admin actions
- Cookie consent (Geo‑aware optional), privacy preferences storage
- Data retention policies for applications, supplier docs

Performance
- SEO & Core Web Vitals: semantic HTML, critical CSS, image lazy‑load, caching headers, gzip, minimal JS
- Page cache for anon pages; cache busting on updates
- Minified assets; CDN friendly

Internationalization (Optional)
- Multi‑language content, locale‑based slugs, hreflang

Integrations
- Razorpay (for merch) — order create, checkout, webhook, refund recording
- Google reCAPTCHA (v2/v3) — Login, Register, Contact, RFQ, Checkout
- Google Analytics 4 — pageview + events (RFQ submit, order, application submit)
- Google Search Console — verification
- Google Maps — store locator
- Email (SMTP) — all notifications

Data Model (Suggested MySQL Tables — abbreviated)
- users (id, name, email, phone, role, password_hash, status, created_at)
- sessions (id, user_id, token, ip, ua, created_at, expires_at)
- pages (id, title, slug, html, status, meta_title, meta_desc, schema_json, published_at)
- posts (id, title, slug, excerpt, content_html, featured_image, status, meta_title, meta_desc, canonical_url, schema_json, published_at)
- categories (id, name, slug, type[post|product|campaign])
- post_categories (post_id, category_id)
- tags (id, name, slug); post_tags (post_id, tag_id)
- media (id, path, alt, mime, size, created_by)
- redirects (id, from_slug, to_url, http_code)
- beverages (id, brand, product_name, sku_code, slug, description, ingredients, allergens, nutrition_json, images_json, status)
- distributors (id, user_id, company, gstin, address_json, tier, credit_limit, status)
- distributor_orders (id, distributor_id, total_amount, status, currency, created_at)
- distributor_order_items (id, order_id, beverage_id, qty, price)
- invoices (id, entity_type, entity_id, number, pdf_path, subtotal, tax, total, issued_at)
- suppliers (id, user_id, company, categories_json, certifications_json, status)
- rfqs (id, title, category, details, due_date, status, created_by)
- rfq_quotes (id, rfq_id, supplier_id, price, lead_time_days, notes, files_json, status)
- purchase_orders (id, supplier_id, rfq_id, total_amount, status, created_at)
- asn (id, po_id, expected_date, items_json, status)
- supplier_invoices (id, po_id, supplier_id, amount, status, pdf_path)
- qa_batches (id, lot_code, product_id, line, produced_at, status)
- qa_tests (id, batch_id, test_name, value, unit, result, tested_at)
- trace_links (id, batch_id, component_type, component_lot, notes)
- merch_products (id, title, slug, price, stock, images_json, status)
- merch_orders (id, user_id, total_amount, status, razorpay_order_id, currency, created_at)
- merch_order_items (id, order_id, product_id, qty, price)
- payments (id, order_type, order_id, razorpay_payment_id, method, amount, status, captured_at, raw_json)
- careers_jobs (id, title, slug, dept, location, type, description_html, status, posted_at)
- careers_applications (id, job_id, name, email, phone, resume_path, status, stage, notes)
- forms_submissions (id, form_type, payload_json, recaptcha_score, created_at)
- settings (key, value_json)
- audit_logs (id, user_id, action, meta_json, created_at)
- email_queue (id, to_email, subject, body_html, status, error, created_at, sent_at)
Indexes: slugs unique; FKs; created_at indexes; appropriate composite keys.

Workflows
- Distributor: registration → admin approval → tier + pricing → orders → invoice → delivery updates → support
- Supplier: sign‑up → prequal → RFQ invite → quote → PO → ASN → invoice → payment status
- QA: create batch → input tests → result pass/fail → generate COA PDF → link to trace lot
- Merch: browse → cart → Razorpay → webhook verify → invoice email
- Careers: post job → receive applications → move stages → email templates
- CMS: create draft → SEO fields → schedule/publish → sitemap/feeds update

SEO
- Clean URLs; per‑page meta; OG/Twitter; schema (Organization, Product, FAQPage, Article, Breadcrumb)
- XML sitemap (pages, posts, beverages, jobs), robots.txt, canonical tags, 301 redirects
- Performance budgets; CLS stable; image alt text; lazy load

Accessibility
- WCAG basics: proper landmarks, labels, color contrast, keyboard nav

Logging & Reporting
- Admin dashboards with charts; CSV export (orders, RFQs, QA tests, applications)
- Error log, access log summaries (privacy‑safe)

Acceptance Criteria
- Public site: product pages with nutrition facts; store locator works
- Distributor portal: order placement and invoice download
- Supplier portal: RFQ → quote → PO flow usable end‑to‑end
- QA: batch record with tests and COA upload/view
- Merch (if enabled): Razorpay end‑to‑end with webhook verification
- CMS/Blog: create/edit/publish with SEO and sitemap updates
- Careers: apply form, applications visible to HR role
- reCAPTCHA blocks spam; rate limiting active on auth/forms
- All sensitive ops audited; RBAC enforced
- Pages pass basic Core Web Vitals on shared hosting

Deployment (cPanel)
- Provide ZIP to upload to public_html; set document root to public/
- Provide SQL dump; import via phpMyAdmin
- Copy config/config.php.example → config.php; set DB, SMTP, GA4, Maps, Razorpay, reCAPTCHA
- .htaccess for routing; ensure AllowOverride All
- Cron: sitemap weekly, email queue every 5 min, backups nightly
- One‑time CLI/URL to create first admin user

Email Templates (SMTP)
- Distributor: order confirmation, invoice ready, delivery update
- Supplier: RFQ invite, PO issued, invoice status
- Merch: order confirmation, payment success/fail, refund notice
- Careers: application received, status updates
- Generic: contact/feedback acknowledgment

Forms & reCAPTCHA Coverage
- Login/Register (all portals), Contact, RFQ submit, Distributor order, Careers apply, Feedback, Merch checkout

Config Placeholders
- DOMAIN: example.com
- COMPANY_NAME, ADDRESS, GSTIN (if applicable)
- SMTP: HOST, PORT, USER, PASS, FROM_EMAIL
- GA4_MEASUREMENT_ID, GSC_VERIFICATION
- MAPS_API_KEY (Google Maps)
- RAZORPAY_KEY_ID, RAZORPAY_KEY_SECRET (if merch enabled)
- RECAPTCHA_SITE_KEY, RECAPTCHA_SECRET, MODE[v2|v3], SCORE_THRESHOLD

Legal & Compliance
- Cookie banner with categories; Do Not Sell (if required)
- Privacy policy aligned to DPDP/GDPR basics; data subject request contact
- Product labeling info aligned to FSSAI (nutritional panel, allergens)

Nice‑to‑Have
- A/B testing for landing blocks (simple split)
- UTM capture on sessions → tie to orders/RFQs/applications
- Internal link suggester in editor; broken links checker
- Multi‑brand theming (Coke, Sprite, Fanta, etc.) via config

Notes
- Use brand/trademarks responsibly; ensure you own rights to logos, imagery, and copy before production.
- Keep code portable for shared hosting; avoid heavy frameworks; composer optional/minimal.

Deliverables
- Production‑ready PHP/MySQL code + README
- Sample data seeders for demo
- Admin user creation script
- Postman collection for APIs (if exposed)
- Verification: run through Acceptance Criteria and provide screenshots/videos.

